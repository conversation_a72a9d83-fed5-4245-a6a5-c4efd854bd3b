JFROG ARTIFACTORY 迁移 APT 的 PROPOSAL
============================================================

迁移到 JFrog 托管的 APT 仓库，能把依赖下载量降一到两个数量级，自动管理版本与漏洞，提高安全合规；成本集中在 CI 打包改造和运维新仓库， 这种流程比较成熟， jfrog天生提供支持。（这里放入参考资料。）

参考资料：https://jfrog.com/integrations/artifactory-debian-repository/

一、Tarball 与 APT 的对比
------------------------------------------------------------
1. Tarball（一个大压缩包）
   - 每次下载整包，体积数百 MB – GB。
   - 解压后所有库混在一起，系统不知道它们之间的依赖关系。
   - 版本回滚或升级只能整包替换，容易牵一发动全身。
2. APT 仓库（.deb 小包集合）
   - 按需下载，平均几十 KB – 几 MB。
   - 操作系统自动解析依赖、检测冲突，可单包升级 / 回滚。
   - 支持 arm64、amd64 等多架构共存，脚本化安装 `apt install xxx`。

二、Tarball 的问题
------------------------------------------------------------
- 大文件下载慢，占 CI 带宽；对于不在公司内网的同学们来说会经常遇到这个问题， 尤其是后面的封闭开发。
- 缺少依赖解析：例如想更新 Eigen，仍需重下整包。
- 文件冲突难排查：详见下节 opencv 例子。
- 难做安全审计：无法快速列出用到的库及版本。

以opencv和opencv contrib为例子， 当两者共存的时候，为什么 opencv / opencv-contrib 会冲突？
------------------------------------------------------------
现象：两个项目都提供 `opencv2/imgproc.hpp`、`libopencv_core.so` 等相同路径/文件名。

• 即便你把 tarball 解压到 `third_party/opencv/` 和 `third_party/opencv_contrib/`，在 CMake 里通常会这样写：
  include_directories(
      third_party/opencv/include
      third_party/opencv_contrib/include)
  link_directories(
      third_party/opencv/lib
      third_party/opencv_contrib/lib)
  compiler 会按搜索顺序找到 **第一个** `opencv2/imgproc.hpp` 就停止，可能拿到 opencv 的旧版本头文件，却在链接阶段拿到 opencv_contrib 的新版本 lib，最终出现符号不匹配、崩溃或难以定位的 bug。

• 如果 opencv_contrib 对某些类做了扩展或 ABI 改动，而你编译时用的是 opencv 的头文件，运行时链接到 opencv_contrib 的库，问题更隐蔽。

APT 打包时，维护者会声明：
  Package: libopencv-contrib-dev
  Conflicts: libopencv-dev (<< 4.8)
系统看到冲突就会阻止同时安装或提示升级，而不会默默覆盖文件。

三、APT 带来的收益
------------------------------------------------------------
- 下载量减少一个数量级，构建时间随之缩短。
- 自动依赖解析与冲突检测，降低人工排错成本。
- 语义化版本（主.次.补丁）+ pin/hold 可锁定关键依赖。
- GPG 签名、JFrog Xray 扫描，自动生成 SBOM， 即软件物料清单，列出所有第三方组件及版本。