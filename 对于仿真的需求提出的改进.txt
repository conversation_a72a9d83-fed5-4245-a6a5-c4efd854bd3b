统一接口层 / 确定性帧驱动架构设计说明（v0.4）
作者：Xiao Yubo
日期：2025-07-02

TLDR：
在保持算法"零侵入"的前提下，通过 Time-Series-Buffer（TSB）+ 单线程 FrameScheduler，可同时锁定【数据时序】与【计算顺序】，从而消除在线/离线随机性，满足 Simulation 组对 bit-level 可复现的强需求，并为跳帧、倍速等仿真高级能力奠定基础。

────────────────────────────────────────────
1  背景
────────────────────────────────────────────
• 现有在线系统基于 Apollo Cyber RT，总线为触发式：每当传感器或子系统产生一帧观测即通过 cyber::Writer 发布，下游模块通过 Callback 回调消费。 
• cyber dispatch 回调的执行顺序受线程池竞争与队列瞬时状态影响，同一物理时刻（逻辑帧）各模块实际读到的观测组合随机，无法离线准确复现。 
• 仿真 / 离线 replay 若仅记录原始 topic 流（bag/record）而不记录消费顺序，无法保证与在线执行结果 bit-level 一致。 
• 目前做法是让每个算法显式维护 depend（channel 名、seq、stamp 等）来描述输入组合，模块多后维护成本陡增且易漏。 

────────────────────────────────────────────
2  目标
────────────────────────────────────────────
A. 在线与离线执行在相同输入下产生完全一致的处理顺序和计算结果。 
B. 算法侧"零侵入"：不再显式维护 depend。 
C. 兼容多分辨率传感器与多 ECU，支持加速 / 跳帧 / Seek 等仿真需求。 
D. 实现简洁、可渐进式迁移。 

────────────────────────────────────────────
3  提案概述（Interface Layer + Deterministic Frame Scheduler）
────────────────────────────────────────────
说明：TSB 即 Time Series Buffer，后文统一简称 TSB。它是按时间戳排序存放观测的环形缓冲区。 

3.1 数据注入
  所有外部传感器与子系统保持原有触发式 publish；Interface Layer 内部统一注册 cyber::Reader，对每条消息写入 TSB。 
  TSB 采用锁-自由环形队列，按 msg->header->timestamp 升序组织，提供按时间窗随机访问。 

3.2 帧调度器（Frame Scheduler）
  • 独立时钟源（可为硬件 PPS 或系统稳态时钟）产生基准节拍 Δt，例如 10 ms（100 Hz）。
  • 每次 tick 生成全局 frame_id，并在同一线程按照模块优先级顺序调用各模块 Process(frame_id, FrameContext)。
  • FrameContext 构造：对每个订阅 topic，从 TSB 取"最近时间戳 ≤ ts_target 且与 ts_target 差值 ≤ 容忍窗 Δw"的最新消息；若缺失则根据策略等待 / 插值 / 丢帧。 
  • 模块在回调里仅依赖 FrameContext，完全无感知线程调度与原始 cyber 队列顺序。 

3.3 记录与回放
  • Recorder 仅记录 <frame_id, topic_hash, seq, stamp, channel_id> 以及 FrameScheduler 配置（Δt, Δw, 初始戳）。
  • 离线时按记录顺序重建 FrameContext，再调用模块，保证与在线同序列。原始大体积的 protobuf 数据可复用已存在的 record 文件或按需截取。 

────────────────────────────────────────────
4  优势与改进点
────────────────────────────────────────────
+ 消除线程竞争随机性，确定性强。 
+ depend 字段外移到框架层，算法零修改。 
+ 录制文件大幅瘦身（只存元信息即可重放顺序）。 
+ 提供"逻辑帧"概念，便于跳帧、倍速播放。 
- 引入 TSB 带来少量延迟与内存开销。 
- 需一次性改造现有 Reader → Interface Layer API，但可分阶段迁移。 


────────────────────────────────────────────
5  结论
────────────────────────────────────────────
本提案通过引入统一接口层及确定性帧调度器，在最小侵入前提下解决现有触发式 callback 随机性带来的离线不可复现问题。TSB (Time Series Buffer) 保证数据时序一致，FrameScheduler 保证调用顺序一致，从而满足 Simulation 组对 determinism 的强需求，推荐尽快启动原型验证。

────────────────────────────────────────────
附录A  为何必须记录 depend / FrameContext
────────────────────────────────────────────
1. 落盘顺序与在线消费顺序天然不一致
   • Cyber Record 进程按照"消息到达总线的时间"顺序串行写磁盘；
   • 在线执行时，同一批消息被线程池并行回调，受队列深度、CPU 抢占等因素影响，**实际消费顺序经常与磁盘顺序不同**；
   • 如果离线仅按磁盘顺序重放，则算法看到的观测组合与线上不同，产生输出漂移。

2. Linux/RTOS 调度非确定性
   • CFS 调度器对同优先级线程采用 vruntime 轮转，存在 <µs 级> 抖动；
   • 中断、软中断、NUMA 迁核、TLB flush 等都会打乱线程执行先后；
   • 即使使用 SCHED_FIFO 仍无法完全消除 PCIe/NIC ISR 及内核抢占带来的乱序。

3. 其它不确定性来源（经常被忽视）
   • 不同 CPU 核心的 TSC 不完全同步，引入纳秒级抖动；
   • 传感器端 DMA FIFO 与网络交换机排队延迟；
   • 分布式 ECU 间 PTP 时钟漂移。

4. FrameContext 的作用
   • 由 **统一 FrameScheduler** 在单线程生成，记录 "模块实际读取的观测集合" (frame_id + <topic, seq, stamp>)；
   • 离线重放时按 FrameContext 顺序调用模块，可完全复现在线执行路径，屏蔽所有上述非确定性。

────────────────────────────────────────────
附录B  TSB (Time Series Buffer) 的来源与业界做法
────────────────────────────────────────────
• 名称为本文自行约定，**概念并非首创**。大量自动驾驶/机器人框架已有类似 "按时间戳聚合 + 集中调度" 设计，典型案例：
  1. NVIDIA DRIVEWorks – Sensor Abstraction Layer (SAL) & dwSensorTimestamp Fusion：官方文档《DRIVE Software Sensors Guide》指出"所有 sensor 数据先进入 ring-buffer，再由 time-synchronized pipeline 拉取"。
  2. Waymo 自研平台 – 在 2020 SIGGRAPH 公开分享 *Building a Scalable Simulation* 中提到 "FrameManager 聚合跨 sensor observation，使用 deterministic tick 推动 pipeline"。
  3. Cruise – US Patent US20210014040A1 描述其"时间对齐缓存 (time-aligned buffer)"以保证 replay 与在线一致性。
  4. Autoware / ROS2 社区 – message_filters::ApproximateTime + Synchronizer 解决多 topic 时序对齐问题，只是粒度更细（回调级），未引入全局 frame_id。
  5. LGSVL Simulator – source 代码里 `SimFrame` 类维护固定 0.02 s tick，将各传感器数据 buffer 后统一推送给车辆插件。

