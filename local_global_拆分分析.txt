面向 ISO 26262 的 Global-/Local-Topic 拆分安全论证

作者: <PERSON> Yubo
日期: 2025-06-28

1 认证目标与范围

项目 / 内容
安全标准:
ISO 26262:2018 – Road Vehicles - Functional Safety
 • Part 3 (概念阶段): https://www.iso.org/standard/68385.html
 • Part 4 (系统级): https://www.iso.org/standard/68386.html
 • Part 6 (软件级): https://www.iso.org/standard/68388.html
 • Part 7 (生产运营): https://www.iso.org/standard/68389.html
认证子系统: Vehicle Localization Sub-system
安全生命周期阶段: 概念 → 系统 → 软件 → 生产运营（对应用 ISO 26262 Part 3/4/6/7）
预期 ASIL: Local Odom 通信链 → ASIL-D; Global Pose 通信链 → ASIL-B
HARA 依据: Local Odom 直接参与车控闭环；失效可能导致车道保持失败 → 高严重度 → ASIL-D。Global Pose 仅用于感知融合 / 远程监控；失效不会立刻危及行车安全 → ASIL-B。

--------------------------------

2 架构措施与 ISO 26262 关联

设计措施 / 对应 ISO 26262 条款 / 说明（术语与来源）
Topic 物理拆分 (/local_odom vs /global_pose) - Part 4 §5.4.5 "ASIL decomposition": 高-ASIL（Local）与低-ASIL（Global）功能分离，满足 Freedom-from-Interference (FFI)，降低验证范围与成本。
QoS 区分 (BEST_EFFORT / RELIABLE) - ISO 26262-6 (软件开发): 要求通信机制与完整性等级匹配; BEST_EFFORT & RELIABLE 源自 OMG DDS 1.4 §7.2 / ROS 2 QoS。BEST_EFFORT: 无重传，低延迟，适合高频 Local 流。RELIABLE: ACK/NACK + 重传，保证完整，适合低频 Global 流。
Safety Channel 仅订阅 Local Odom - ISO 26262-6 Freedom-from-Interference: Apollo Cyber 的 Safety Channel 只读取 ASIL-D 数据，Global 流崩溃不影响控制链。
Watch-Dog & Heartbeat 监控 - ISO 26262-6 Error handling / Safe state: 50 ms 内检测 Local Odom 超时并切入 Encoder-Only Fallback ⇒ Fail-operate（故障后仍可受控运行）。
CRC + Seq Counter (E2E Profile 2/4) - Part 6 §9.4.5.5 "Data integrity mechanisms": AUTOSAR E2E Library v1.3; P2 – 16-bit CRC + 8-bit Seq，适合高速单帧 (Local)。P4 – 分段 增量 CRC，适合低频大帧 (Global)。
TSN / CAN-FD 网络隔离 - Part 8 §12.4.2 Freedom from interference – HW resource: 高速 Local Odom 走 CAN-FD 2 Mbps；低频 Global Pose 走 TSN 1 GbE，减少带宽/抖动互扰。

--------------------------------

3 术语小词典

BEST_EFFORT / RELIABLE (OMG DDS 1.4 §7.2; ROS 2 Design Docs): DDS 可靠性 QoS: BEST_EFFORT不保证重传；RELIABLE 通过确认-重传保证到达。
Freedom-from-Interference (FFI) (ISO 26262-8 §12): 不同 ASIL 软件元素/硬件资源不能相互破坏或影响。
Fail-operate (Fail-operational) (ISO 26262 通用术语): 系统在检测到故障后可在受控或降级模式继续运行。
AUTOSAR E2E P2 / P4 (AUTOSAR E2E Library v1.3): P2—高速单帧保护；P4—多帧增量 CRC，适合大报文。
增量 CRC (AUTOSAR E2E P4): 将大帧分段计算 CRC，接收端可逐段校验，降低一次性延迟。

--------------------------------

4 进一步阅读与参考链接

标准: ISO 26262 官方购买页 - https://www.iso.org/iso-26262
规范: OMG DDS v1.4 - https://www.omg.org/spec/DDS/1.4
指南: ROS 2 QoS Policies - https://docs.ros.org/en/foxy/Concepts/About-Quality-of-Service-Settings.html
白皮书: Apex.AI - Designing ASIL-D Communication over ROS 2 (2022) - https://www.apex.ai/wp-content/uploads/2022/10/apex_whitepaper_ros2_safety.pdf
指南: AUTOSAR End-to-End Library v1.3 - https://www.autosar.org/fileadmin/standards/adaptive/22-11/AR-E2ELibrary.pdf
实践: Apollo Cyber Safety Channel - https://github.com/ApolloAuto/apollo/tree/master/cyber/safety

--------------------------------

5 小结

通过 Topic 拆分 + QoS 区分 + 网络/软件隔离，可在体系结构层面完成 ASIL 分解与 FFI 论证，为后续 ISO 26262 认证提供可审计的设计证据与实现基础。 